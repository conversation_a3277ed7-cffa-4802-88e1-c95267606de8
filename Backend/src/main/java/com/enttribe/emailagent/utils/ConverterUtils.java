package com.enttribe.emailagent.utils;

import com.enttribe.emailagent.dto.AttendeeAndStatus;
import com.enttribe.emailagent.dto.EventDto;
import lombok.extern.slf4j.Slf4j;
import microsoft.exchange.webservices.data.core.service.item.Appointment;
import microsoft.exchange.webservices.data.property.complex.AttendeeCollection;
import microsoft.exchange.webservices.data.property.complex.EmailAddress;

import java.util.List;

@Slf4j
public class ConverterUtils {


    public static EventDto convertToEventDto(Appointment appointment) throws Exception {
        EventDto eventDto = new EventDto();

        eventDto.setId(appointment.getId().toString());
        eventDto.setOrganizer(appointment.getOrganizer().getAddress());
        AttendeeCollection requiredAttendees = appointment.getRequiredAttendees();
        AttendeeCollection optionalAttendees = appointment.getOptionalAttendees();
        List<String> list = requiredAttendees.getItems().stream().map(EmailAddress::getAddress).toList();
        List<AttendeeAndStatus> required = requiredAttendees.getItems().stream().map(attendee -> new AttendeeAndStatus(attendee.getAddress(), attendee.getResponseType().toString())).toList();
        List<AttendeeAndStatus> optional = optionalAttendees.getItems().stream().map(attendee -> new AttendeeAndStatus(attendee.getAddress(), attendee.getResponseType().toString())).toList();
        eventDto.setAttendees(list);
        eventDto.setSubject(appointment.getSubject());
        String bodyPreview;
        try {
            bodyPreview = appointment.getBody().toString();
        } catch (Exception e) {
            bodyPreview = null;
        }
        eventDto.setBodyPreview(bodyPreview);
        eventDto.setJoinUrl(appointment.getMeetingWorkspaceUrl());
        eventDto.setHasAttachments(appointment.getHasAttachments());
        eventDto.setMeetingStartTime(appointment.getStart());
        eventDto.setMeetingEndTime(appointment.getEnd());
        eventDto.setCreatedDateTime(appointment.getDateTimeCreated());
        eventDto.setLastModifiedDateTime(appointment.getLastModifiedTime());
        eventDto.setRequiredAttendees(required);
        eventDto.setOptionalAttendees(optional);
        eventDto.setLocation(appointment.getLocation());
        log.info("Response is {}",appointment.getMyResponseType().toString());
        eventDto.setAccepted(appointment.getMyResponseType().toString());
        return eventDto;
    }
}
